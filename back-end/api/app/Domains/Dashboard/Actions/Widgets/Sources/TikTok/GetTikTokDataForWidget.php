<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\TikTok;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\TikTok\Models\TikTokAccount;
use App\Domains\Sources\TikTok\Models\TikTokVideoInsight;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class GetTikTokDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function __construct() {}

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetAnalyticsForDataSet($this->getData());
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter, WidgetDetailScope $widgetDetailScope): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $detailKey = match ($widgetDetailScope) {
            WidgetDetailScope::DATASOURCE => 'title',
            WidgetDetailScope::REGION => 'region',
            WidgetDetailScope::BUSINESS_UNIT => 'business_unit',
        };

        $reportData = $this->getData($detailKey);
        $reportDataPerDetailKey = [];

        foreach ($reportData as $dataPoint) {
            if (! isset($reportDataPerDetailKey[$dataPoint->detailKey])) {
                $reportDataPerDetailKey[$dataPoint->detailKey] = new Collection;
            }

            $reportDataPerDetailKey[$dataPoint->detailKey]->push($dataPoint);
        }

        foreach ($reportDataPerDetailKey as $detailValue => $dataSet) {
            $reportDataPerDetailKey[$detailValue] = $this->getWidgetAnalyticsForDataSet($dataSet, $detailKey, $detailValue);
        }

        return $reportDataPerDetailKey;
    }

    protected function getWidgetAnalyticsForDataSet(Collection $dataSet, ?string $detailKey = null, ?string $detailValue = null): WidgetAnalytics
    {
        $formattedData = [];
        $numerator = 0;
        $denominator = 0;
        $multiplier = 1;
        $decimals = 0;

        foreach ($dataSet as $dataPoint) {
            $value = 0;

            switch ($this->widget->type) {
                case WidgetType::TIKTOK_VIDEO_VIEWS:
                    $value = (float) $dataPoint->views;
                    $numerator += (float) $dataPoint->views;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::TIKTOK_VIDEO_ENGAGEMENT:
                    $value = (float) $dataPoint->engagement;
                    $numerator += (float) $dataPoint->engagement;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::TIKTOK_VIDEO_ENGAGEMENT_RATE:
                    $value = $dataPoint->views ? ($dataPoint->engagement / $dataPoint->views) * 100 : 0;
                    $numerator += (float) $dataPoint->engagement;
                    $denominator += (float) $dataPoint->views;
                    $multiplier = 100;
                    $decimals = 2;
                    break;
            }

            $widgetAnalyticsDataPoint = new WidgetAnalyticsDataPoint;
            $widgetAnalyticsDataPoint->setDate($dataPoint->date);
            $widgetAnalyticsDataPoint->setValue(round($value, 3));
            $widgetAnalyticsDataPoint->setValueFormatted($this->formatValue($value, $decimals));

            $formattedData[] = $widgetAnalyticsDataPoint;
        }

        $comparisonTotal = $this->getComparisonDataTotal($detailKey, $detailValue);

        $total = $denominator ? ($numerator / $denominator) * $multiplier : 0;

        $widgetAnalytics = new WidgetAnalytics;
        $widgetAnalytics->setData($formattedData);
        $widgetAnalytics->setTotal($total);
        $widgetAnalytics->setTotalFormatted($this->formatValue($total, $decimals));
        $widgetAnalytics->setComparison($comparisonTotal ?? null);
        $widgetAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal, $decimals) : null);
        $widgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return $widgetAnalytics;
    }

    protected function getData(?string $detailKey = null): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(tik_tok_video_insights.date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(tik_tok_video_insights.date, "%Y-%m")',
        };

        $baseQuery = TikTokVideoInsight::query()
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('SUM(view_count) as views')
            ->selectRaw('(SUM(comment_count) + SUM(share_count) + SUM(like_count)) as engagement')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('tik_tok_videos', 'tik_tok_video_insights.tik_tok_video_id', '=', 'tik_tok_videos.id')
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on('data_sources.sourceable_id', '=', 'tik_tok_videos.tik_tok_account_id')
                        ->where('data_sources.sourceable_type', '=', TikTokAccount::class);
                })
                ->orderByRaw(sprintf('data_sources.%s ASC', $detailKey))
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if ($this->filter->getStartDate()) {
            $baseQuery->where('tik_tok_video_insights.date', '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('tik_tok_video_insights.date', '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }

    protected function getComparisonDataTotal(?string $detailKey = null, ?string $detailValue = null): ?float
    {
        $baseQuery = TikTokVideoInsight::query()
            ->selectRaw('SUM(view_count) as views')
            ->selectRaw('(SUM(comment_count) + SUM(share_count) + SUM(like_count)) as engagement')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('tik_tok_videos', 'tik_tok_video_insights.tik_tok_video_id', '=', 'tik_tok_videos.id')
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on('data_sources.sourceable_id', '=', 'tik_tok_videos.tik_tok_account_id')
                        ->where('data_sources.sourceable_type', '=', TikTokAccount::class);
                })
                ->where(sprintf('data_sources.%s', $detailKey), $detailValue)
                ->orderByRaw(sprintf('data_sources.%s ASC', $detailKey))
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if (! $this->filter->getComparableStartDate() && ! $this->filter->getComparableEndDate()) {
            return null;
        }

        $baseQuery
            ->whereBetween('tik_tok_video_insights.date', [
                $this->filter->getComparableStartDate(),
                $this->filter->getComparableEndDate(),
            ])
            ->groupByRaw('tik_tok_video_insights.tik_tok_video_id');

        $dataPoints = $baseQuery->get();
        $total = 0;

        if (! $dataPoints) {
            return $total;
        }

        switch ($this->widget->type) {
            case WidgetType::TIKTOK_VIDEO_VIEWS:
                $total = $dataPoints->sum('views');
                break;
            case WidgetType::TIKTOK_VIDEO_ENGAGEMENT:
                $total = $dataPoints->sum('engagement');
                break;
            case WidgetType::TIKTOK_VIDEO_ENGAGEMENT_RATE:
                $total = $dataPoints->sum('engagement') ? ($dataPoints->sum('views') / $dataPoints->sum('engagement')) * 100 : 0;
                break;
        }

        return (float) $total;
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
