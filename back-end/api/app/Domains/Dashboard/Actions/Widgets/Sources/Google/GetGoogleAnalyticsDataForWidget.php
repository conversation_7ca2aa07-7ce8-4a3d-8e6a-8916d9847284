<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\Google;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Google\Models\GoogleAnalyticsProperty;
use App\Domains\Sources\Google\Models\GoogleAnalyticsReport;
use App\Domains\Sources\Google\Support\Enums\Analytics\ReportType;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class GetGoogleAnalyticsDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetAnalyticsForDataSet($this->getGoogleAnalyticsReportData());
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter, WidgetDetailScope $widgetDetailScope): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $detailKey = match ($widgetDetailScope) {
            WidgetDetailScope::DATASOURCE => 'title',
            WidgetDetailScope::REGION => 'region',
            WidgetDetailScope::BUSINESS_UNIT => 'business_unit',
        };

        $googleAnalyticsReportData = $this->getGoogleAnalyticsReportData($detailKey);
        $reportDataPerDetailKey = [];

        foreach ($googleAnalyticsReportData as $dataPoint) {
            if (! isset($reportDataPerDetailKey[$dataPoint->detailKey])) {
                $reportDataPerDetailKey[$dataPoint->detailKey] = new Collection;
            }

            $reportDataPerDetailKey[$dataPoint->detailKey]->push($dataPoint);
        }

        foreach ($reportDataPerDetailKey as $detailValue => $dataSet) {
            $reportDataPerDetailKey[$detailValue] = $this->getWidgetAnalyticsForDataSet($dataSet, $detailKey, $detailValue);
        }

        return $reportDataPerDetailKey;
    }

    protected function getWidgetAnalyticsForDataSet(Collection $dataSet, ?string $detailKey = null, ?string $detailValue = null): WidgetAnalytics
    {
        $formattedData = [];
        $subTotal = 0;

        foreach ($dataSet as $dataPoint) {
            $value = (float) $dataPoint->value;
            $subTotal += $value;

            $widgetAnalyticsDataPoint = new WidgetAnalyticsDataPoint;
            $widgetAnalyticsDataPoint->setDate($dataPoint->date); // TODO: Do something with timezone
            $widgetAnalyticsDataPoint->setValue($value);
            $widgetAnalyticsDataPoint->setValueFormatted($this->formatValue($value));

            $formattedData[] = $widgetAnalyticsDataPoint;
        }

        $comparisonTotal = $this->getComparisonDataTotal($detailKey, $detailValue);

        $widgetAnalytics = new WidgetAnalytics;
        $widgetAnalytics->setData($formattedData);
        $widgetAnalytics->setTotal($subTotal);
        $widgetAnalytics->setTotalFormatted($this->formatValue($subTotal));
        $widgetAnalytics->setComparison($comparisonTotal ?? null);
        $widgetAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal) : null);
        $widgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return $widgetAnalytics;
    }

    protected function getGoogleAnalyticsReportData(?string $detailKey = null): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(date, "%Y-%m")',
        };

        $baseQuery = GoogleAnalyticsReport::query()
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('SUM(value) as value')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->where('type', $this->getReportType()->value)
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on(
                            first: 'data_sources.sourceable_id',
                            operator: '=',
                            second: 'google_analytics_reports.google_analytics_property_id'
                        )
                        ->where(
                            column: 'data_sources.sourceable_type',
                            operator: '=',
                            value: GoogleAnalyticsProperty::class
                        );
                })
                ->orderByRaw(sprintf('data_sources.%s ASC', $detailKey))
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if ($this->filter->getStartDate()) {
            $baseQuery->where('date', '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('date', '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }

    protected function getComparisonDataTotal(?string $detailKey = null, ?string $detailValue = null): ?float
    {
        $baseQuery = GoogleAnalyticsReport::query()
            ->selectRaw('SUM(value) as value')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->where('type', $this->getReportType()->value);

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on(
                            first: 'data_sources.sourceable_id',
                            operator: '=',
                            second: 'google_analytics_reports.google_analytics_property_id'
                        )
                        ->where(
                            column: 'data_sources.sourceable_type',
                            operator: '=',
                            value: GoogleAnalyticsProperty::class
                        );
                })
                ->where(sprintf('data_sources.%s', $detailKey), $detailValue)
                ->orderByRaw(sprintf('data_sources.%s ASC', $detailKey))
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if (! $this->filter->getComparableStartDate() && ! $this->filter->getComparableEndDate()) {
            return null;
        }

        $baseQuery->whereBetween('date', [
            $this->filter->getComparableStartDate(),
            $this->filter->getComparableEndDate(),
        ]);

        $total = $baseQuery->first()?->value ?? 0;

        return (float) $total;
    }

    protected function formatValue(float $value): string
    {
        return number_format(
            num: $value,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }

    protected function getReportType(): ReportType
    {
        return match ($this->widget->type) {
            WidgetType::GOOGLE_ANALYTICS_SESSION => ReportType::SESSION,
            WidgetType::GOOGLE_ANALYTICS_BOOKINGS => ReportType::BOOKING,
        };
    }
}
