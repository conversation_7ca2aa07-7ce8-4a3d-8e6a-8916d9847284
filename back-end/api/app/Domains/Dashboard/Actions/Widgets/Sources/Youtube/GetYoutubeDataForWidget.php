<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\Youtube;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Google\Models\YoutubeChannelInsight;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class GetYoutubeDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function __construct() {}

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetAnalyticsForDataSet($this->getYoutubeData());
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter, WidgetDetailScope $widgetDetailScope): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $detailKey = match ($widgetDetailScope) {
            WidgetDetailScope::DATASOURCE => 'title',
            WidgetDetailScope::REGION => 'region',
            WidgetDetailScope::BUSINESS_UNIT => 'business_unit',
        };

        $googleAnalyticsReportData = $this->getYoutubeData($detailKey);
        $reportDataPerDetailKey = [];

        foreach ($googleAnalyticsReportData as $dataPoint) {
            if (! isset($reportDataPerDetailKey[$dataPoint->detailKey])) {
                $reportDataPerDetailKey[$dataPoint->detailKey] = new Collection;
            }

            $reportDataPerDetailKey[$dataPoint->detailKey]->push($dataPoint);
        }

        foreach ($reportDataPerDetailKey as $detailValue => $dataSet) {
            $reportDataPerDetailKey[$detailValue] = $this->getWidgetAnalyticsForDataSet($dataSet, $detailKey, $detailValue);
        }

        return $reportDataPerDetailKey;
    }

    protected function getWidgetAnalyticsForDataSet(Collection $dataSet, ?string $detailKey = null, ?string $detailValue = null): WidgetAnalytics
    {
        $formattedData = [];
        $numerator = 0;
        $denominator = 0;
        $multiplier = 1;
        $decimals = 0;

        foreach ($dataSet as $dataPoint) {
            $value = 0;

            switch ($this->widget->type) {
                case WidgetType::YOUTUBE_VIEWS:
                    $value = (float) $dataPoint->views;
                    $numerator += (float) $dataPoint->views;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::YOUTUBE_ENGAGEMENT:
                    $value = (float) $dataPoint->engagement;
                    $numerator += (float) $dataPoint->engagement;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::YOUTUBE_ENGAGEMENT_RATE:
                    $value = $dataPoint->views ? ($dataPoint->engagement / $dataPoint->views) * 100 : 0;
                    $numerator += (float) $dataPoint->engagement;
                    $denominator += (float) $dataPoint->views;
                    $multiplier = 100;
                    $decimals = 2;
                    break;
                case WidgetType::YOUTUBE_NUMBER_OF_VIDEOS:
                    $value = (float) $dataPoint->number_of_videos;
                    $numerator += (float) $dataPoint->number_of_videos;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::YOUTUBE_NUMBER_OF_SUBSCRIBERS:
                    $value = (float) $dataPoint->subscriber_count;
                    $numerator = (float) $dataPoint->subscriber_count;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
            }

            $widgetAnalyticsDataPoint = new WidgetAnalyticsDataPoint;
            $widgetAnalyticsDataPoint->setDate($dataPoint->date);
            $widgetAnalyticsDataPoint->setValue(round($value, 3));
            $widgetAnalyticsDataPoint->setValueFormatted($this->formatValue($value, $decimals));

            $formattedData[] = $widgetAnalyticsDataPoint;
        }

        $comparisonTotal = $this->getComparisonDataTotal($detailKey, $detailValue);

        $total = $denominator ? ($numerator / $denominator) * $multiplier : 0;

        $widgetAnalytics = new WidgetAnalytics;
        $widgetAnalytics->setData($formattedData);
        $widgetAnalytics->setTotal($total);
        $widgetAnalytics->setTotalFormatted($this->formatValue($total, $decimals));
        $widgetAnalytics->setComparison($comparisonTotal ?? null);
        $widgetAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal, $decimals) : null);
        $widgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return $widgetAnalytics;
    }

    protected function getYoutubeData(?string $detailKey = null): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(date, "%Y-%m")',
        };

        $baseQuery = YoutubeChannelInsight::query()
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('SUM(views) as views')
            ->selectRaw('SUM(engagement) as engagement')
            ->selectRaw('SUM(number_of_videos) as number_of_videos')
            ->selectRaw('SUM(subscriber_count) as subscriber_count')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on(
                            first: 'data_sources.sourceable_id',
                            operator: '=',
                            second: 'youtube_channel_insights.youtube_channel_id'
                        )
                        ->where(
                            column: 'data_sources.sourceable_type',
                            operator: '=',
                            value: YoutubeChannel::class
                        );
                })
                ->orderByRaw(sprintf('data_sources.%s ASC', $detailKey))
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if ($this->filter->getStartDate()) {
            $baseQuery->where('date', '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('date', '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }

    protected function getComparisonDataTotal(?string $detailKey = null, ?string $detailValue = null): ?float
    {
        $baseQuery = YoutubeChannelInsight::query()
            ->selectRaw('SUM(views) as views')
            ->selectRaw('SUM(engagement) as engagement')
            ->selectRaw('SUM(number_of_videos) as number_of_videos')
            ->selectRaw('AVG(subscriber_count) as subscriber_count')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on(
                            first: 'data_sources.sourceable_id',
                            operator: '=',
                            second: 'youtube_channel_insights.youtube_channel_id'
                        )
                        ->where(
                            column: 'data_sources.sourceable_type',
                            operator: '=',
                            value: YoutubeChannel::class
                        );
                })
                ->where(sprintf('data_sources.%s', $detailKey), $detailValue)
                ->orderByRaw(sprintf('data_sources.%s ASC', $detailKey))
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if (! $this->filter->getComparableStartDate() && ! $this->filter->getComparableEndDate()) {
            return null;
        }

        $baseQuery
            ->whereBetween('date', [
                $this->filter->getComparableStartDate(),
                $this->filter->getComparableEndDate(),
            ])
            ->groupByRaw('youtube_channel_insights.youtube_channel_id');

        $dataPoints = $baseQuery->get();
        $total = 0;

        if (! $dataPoints) {
            return $total;
        }

        switch ($this->widget->type) {
            case WidgetType::YOUTUBE_VIEWS:
                $total = $dataPoints->sum('views');
                break;
            case WidgetType::YOUTUBE_ENGAGEMENT:
                $total = $dataPoints->sum('engagement');
                break;
            case WidgetType::YOUTUBE_ENGAGEMENT_RATE:
                $total = $dataPoints->sum('engagement') ? ($dataPoints->sum('engagement') / $dataPoints->sum('views')) * 100 : 0;
                break;
            case WidgetType::YOUTUBE_NUMBER_OF_VIDEOS:
                $total = $dataPoints->sum('number_of_videos');
                break;
            case WidgetType::YOUTUBE_NUMBER_OF_SUBSCRIBERS:
                $total = $dataPoints->sum('subscriber_count');
                break;
        }

        return (float) $total;
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
