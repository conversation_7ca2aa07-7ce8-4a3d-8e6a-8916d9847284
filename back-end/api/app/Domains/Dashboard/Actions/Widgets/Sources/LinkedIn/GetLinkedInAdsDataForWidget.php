<?php

declare(strict_types=1);

namespace App\Domains\Dashboard\Actions\Widgets\Sources\LinkedIn;

use App\Domains\Dashboard\Actions\Widgets\GetDataSourceIdsForWidget;
use App\Domains\Dashboard\Models\Widget;
use App\Domains\Dashboard\Support\Dto\WidgetAnalytics;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsDataPoint;
use App\Domains\Dashboard\Support\Dto\WidgetAnalyticsFilter;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetAccuracy;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetDetailScope;
use App\Domains\Dashboard\Support\Enums\Widget\WidgetType;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdInsight;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class GetLinkedInAdsDataForWidget
{
    protected Widget $widget;

    protected WidgetAnalyticsFilter $filter;

    public function __construct() {}

    public function single(Widget $widget, WidgetAnalyticsFilter $filter): WidgetAnalytics
    {
        $this->widget = $widget;
        $this->filter = $filter;

        return $this->getWidgetAnalyticsForDataSet($this->getLinkedInAdsData());
    }

    /**
     * @return array<string, WidgetAnalytics>
     */
    public function detail(Widget $widget, WidgetAnalyticsFilter $filter, WidgetDetailScope $widgetDetailScope): array
    {
        $this->widget = $widget;
        $this->filter = $filter;

        $detailKey = match ($widgetDetailScope) {
            WidgetDetailScope::DATASOURCE => 'title',
            WidgetDetailScope::REGION => 'region',
            WidgetDetailScope::BUSINESS_UNIT => 'business_unit',
        };

        $linkedInAdsData = $this->getLinkedInAdsData($detailKey);
        $reportDataPerDetailKey = [];

        foreach ($linkedInAdsData as $dataPoint) {
            if (! isset($reportDataPerDetailKey[$dataPoint->detailKey])) {
                $reportDataPerDetailKey[$dataPoint->detailKey] = new Collection;
            }

            $reportDataPerDetailKey[$dataPoint->detailKey]->push($dataPoint);
        }

        foreach ($reportDataPerDetailKey as $detailValue => $dataSet) {
            $reportDataPerDetailKey[$detailValue] = $this->getWidgetAnalyticsForDataSet($dataSet, $detailKey, $detailValue);
        }

        return $reportDataPerDetailKey;
    }

    protected function getWidgetAnalyticsForDataSet(Collection $dataSet, ?string $detailKey = null, ?string $detailValue = null): WidgetAnalytics
    {
        $formattedData = [];
        $numerator = 0;
        $denominator = 0;
        $multiplier = 1;
        $decimals = 0;

        foreach ($dataSet as $dataPoint) {
            $value = 0;

            switch ($this->widget->type) {
                case WidgetType::LINKED_IN_ADS_REACH:
                    $value = (float) $dataPoint->reach;
                    $numerator += (float) $dataPoint->reach;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
                case WidgetType::LINKED_IN_ADS_SPEND:
                    $value = (float) $dataPoint->spend;
                    $numerator += (float) $dataPoint->spend;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 2;
                    break;
                case WidgetType::LINKED_IN_ADS_CLICK_THROUGH_RATE:
                    $value = $dataPoint->impressions ? ($dataPoint->clicks / $dataPoint->impressions) * 100 : 0;
                    $numerator += (float) $dataPoint->clicks;
                    $denominator += (float) $dataPoint->impressions;
                    $multiplier = 100;
                    $decimals = 2;
                    break;
                case WidgetType::LINKED_IN_ADS_COST_PER_MILE:
                    $value = $dataPoint->impressions ? ($dataPoint->spend / $dataPoint->impressions) * 1000 : 0;
                    $numerator += (float) $dataPoint->spend;
                    $denominator += (float) $dataPoint->impressions;
                    $multiplier = 1000;
                    $decimals = 2;
                    break;
                case WidgetType::LINKED_IN_ADS_CLICKS:
                    $value = (float) $dataPoint->clicks;
                    $numerator += (float) $dataPoint->clicks;
                    $denominator = 1;
                    $multiplier = 1;
                    $decimals = 0;
                    break;
            }

            $widgetAnalyticsDataPoint = new WidgetAnalyticsDataPoint;
            $widgetAnalyticsDataPoint->setDate($dataPoint->date);
            $widgetAnalyticsDataPoint->setValue(round($value, 3));
            $widgetAnalyticsDataPoint->setValueFormatted($this->formatValue($value, $decimals));

            $formattedData[] = $widgetAnalyticsDataPoint;
        }

        $comparisonTotal = $this->getComparisonDataTotal($detailKey, $detailValue);

        $total = $denominator ? ($numerator / $denominator) * $multiplier : 0;

        $widgetAnalytics = new WidgetAnalytics;
        $widgetAnalytics->setData($formattedData);
        $widgetAnalytics->setTotal($total);
        $widgetAnalytics->setTotalFormatted($this->formatValue($total, $decimals));
        $widgetAnalytics->setComparison($comparisonTotal ?? null);
        $widgetAnalytics->setComparisonFormatted($comparisonTotal ? $this->formatValue($comparisonTotal, $decimals) : null);
        $widgetAnalytics->setWidgetAccuracy($this->filter->getAccuracy());

        return $widgetAnalytics;
    }

    protected function getLinkedInAdsData(?string $detailKey = null): Collection
    {
        $dateSelector = match ($this->filter->getAccuracy()) {
            WidgetAccuracy::DAY => 'DATE_FORMAT(date, "%Y-%m-%d")',
            WidgetAccuracy::MONTH => 'DATE_FORMAT(date, "%Y-%m")',
        };

        $baseQuery = LinkedInAdInsight::query()
            ->selectRaw(sprintf('%s as date', $dateSelector))
            ->selectRaw('SUM(reach) as reach')
            ->selectRaw('SUM(spend) as spend')
            ->selectRaw('SUM(clicks) as clicks')
            ->selectRaw('SUM(impressions) as impressions')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter))
            ->groupByRaw(sprintf('%s', $dateSelector));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('linked_in_ad_campaigns', 'linked_in_ad_insights.linkedin_ad_campaign_id', '=', 'linked_in_ad_campaigns.id')
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on('data_sources.sourceable_id', '=', 'linked_in_ad_campaigns.linkedin_ad_account_id')
                        ->where('data_sources.sourceable_type', '=', LinkedInAdAccount::class);
                })
                ->orderByRaw(sprintf('data_sources.%s ASC', $detailKey))
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if ($this->filter->getStartDate()) {
            $baseQuery->where('date', '>=', $this->filter->getStartDate());
        }

        if ($this->filter->getEndDate()) {
            $baseQuery->where('date', '<=', $this->filter->getEndDate());
        }

        return $baseQuery->get();
    }

    protected function getComparisonDataTotal(?string $detailKey = null, ?string $detailValue = null): ?float
    {
        $baseQuery = LinkedInAdInsight::query()
            ->selectRaw('SUM(reach) as reach')
            ->selectRaw('SUM(spend) as spend')
            ->selectRaw('SUM(clicks) as clicks')
            ->selectRaw('SUM(impressions) as impressions')
            ->withDataSources(app(GetDataSourceIdsForWidget::class)->execute($this->widget, $this->filter));

        if ($detailKey) {
            $baseQuery
                ->selectRaw(sprintf('data_sources.%s as detailKey', $detailKey))
                ->leftJoin('linked_in_ad_campaigns', 'linked_in_ad_insights.linkedin_ad_campaign_id', '=', 'linked_in_ad_campaigns.id')
                ->leftJoin('data_sources', function (JoinClause $join) {
                    $join
                        ->on('data_sources.sourceable_id', '=', 'linked_in_ad_campaigns.linkedin_ad_account_id')
                        ->where('data_sources.sourceable_type', '=', LinkedInAdAccount::class);
                })
                ->where(sprintf('data_sources.%s', $detailKey), $detailValue)
                ->orderByRaw(sprintf('data_sources.%s ASC', $detailKey))
                ->groupByRaw(sprintf('data_sources.%s', $detailKey));
        }

        if (! $this->filter->getComparableStartDate() && ! $this->filter->getComparableEndDate()) {
            return null;
        }

        $baseQuery->whereBetween('date', [
            $this->filter->getComparableStartDate(),
            $this->filter->getComparableEndDate(),
        ]);

        $dataPoint = $baseQuery->first();
        $total = 0;

        switch ($this->widget->type) {
            case WidgetType::LINKED_IN_ADS_REACH:
                $total = (float) $dataPoint->reach;
                break;
            case WidgetType::LINKED_IN_ADS_SPEND:
                $total = (float) $dataPoint->spend;
                break;
            case WidgetType::LINKED_IN_ADS_CLICKS:
                $total = (float) $dataPoint->clicks;
                break;
            case WidgetType::LINKED_IN_ADS_CLICK_THROUGH_RATE:
                $total = $dataPoint->impressions ? ($dataPoint->clicks / $dataPoint->impressions) * 100 : 0;
                break;
            case WidgetType::LINKED_IN_ADS_COST_PER_MILE:
                $total = $dataPoint->impressions ? ($dataPoint->spend / $dataPoint->impressions) * 1000 : 0;
                break;
        }

        return (float) $total;
    }

    protected function formatValue(float $value, int $decimals = 0): string
    {
        return number_format(
            num: $value,
            decimals: $decimals,
            decimal_separator: ',',
            thousands_separator: '.'
        );
    }
}
