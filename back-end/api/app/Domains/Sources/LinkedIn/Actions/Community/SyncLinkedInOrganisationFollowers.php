<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Actions\Community;

use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationInsight;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Community\CannotGetLinkedInOrganisationFollowers;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class SyncLinkedInOrganisationFollowers
{
    public function execute(LinkedInOrganisation $organisation): void
    {
        $response = $this->getResponse($organisation);
        $this->processFollowerStats($organisation, $response->json());
    }

    protected function processFollowerStats(LinkedInOrganisation $organisation, array $data): void
    {
        if (empty($data['elements'])) {
            return;
        }

        $stats = $data['elements'][0];
        $totalFollowers = 0;

        // Sum up organic followers from all industries
        foreach ($stats['followerCountsByIndustry'] as $industry) {
            $totalFollowers += $industry['followerCounts']['organicFollowerCount'] ?? 0;
        }

        // Create or update insight for today
        LinkedInOrganisationInsight::withTrashed()->updateOrCreate(
            [
                'linkedin_organisation_id' => $organisation->id,
                'date' => now()->startOfDay(),
            ],
            [
                'followers' => $totalFollowers,
                'deleted_at' => null,
            ]
        );
    }

    protected function getResponse(LinkedInOrganisation $organisation): Response
    {
        $token = app(RefreshToken::class)->execute($organisation->linkedInAccount);

        if (! $token) {
            throw CannotGetLinkedInOrganisationFollowers::becauseOfMissingToken($organisation);
        }

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$token}",
            'LinkedIn-Version' => '202501',
            'X-Restli-Protocol-Version' => '1.0.0',
        ])->get('https://api.linkedin.com/rest/organizationalEntityFollowerStatistics', [
            'q' => 'organizationalEntity',
            'organizationalEntity' => "urn:li:organization:{$organisation->external_id}",
        ]);

        if (! $response->successful()) {
            throw CannotGetLinkedInOrganisationFollowers::becauseOfHttpErrorWithStatusCode(
                organisation: $organisation,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
