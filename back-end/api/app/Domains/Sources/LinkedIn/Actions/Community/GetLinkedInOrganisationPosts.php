<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Actions\Community;

use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisation;
use App\Domains\Sources\LinkedIn\Models\LinkedInOrganisationPost;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Community\CannotGetLinkedInOrganisationPosts;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class GetLinkedInOrganisationPosts
{
    public function execute(LinkedInOrganisation $organisation, ?int $start = 0): ?int
    {
        $response = $this->getResponse($organisation, $start);
        $posts = $response->json('elements', []);

        $this->processPosts(
            organisation: $organisation,
            posts: $posts
        );

        return $this->getNextPageStart($response);
    }

    protected function getNextPageStart(Response $response): ?int
    {
        $paging = $response->json('paging', []);
        $currentStart = $paging['start'] ?? 0;
        $count = $paging['count'] ?? 0;
        $total = $paging['total'] ?? 0;

        return ($currentStart + $count) < $total ? $currentStart + $count : null;
    }

    protected function processPosts(
        LinkedInOrganisation $organisation,
        array $posts
    ): void {
        foreach ($posts as $post) {
            $this->processPost($organisation, $post);
        }
    }

    protected function processPost(
        LinkedInOrganisation $organisation,
        array $post
    ): void {
        $timestamp = $post['publishedAt'] ?? $post['createdAt'];
        $date = Carbon::createFromTimestampMs($timestamp);

        LinkedInOrganisationPost::withTrashed()->updateOrCreate(
            [
                'linkedin_organisation_id' => $organisation->id,
                'external_id' => $post['id'],
            ],
            [
                'date' => $date,
                'state' => $post['lifecycleState'],
                'visibility' => $post['visibility'],
                'commentary_excerpt' => Str::limit($post['commentary'] ?? '', 255),
                'deleted_at' => null,
            ]
        );
    }

    /**
     * @throws CannotGetLinkedInOrganisationPosts
     * @throws ConnectionException
     */
    protected function getResponse(LinkedInOrganisation $organisation, ?int $start): Response
    {
        $token = app(RefreshToken::class)->execute($organisation->linkedInAccount);

        if (! $token) {
            throw CannotGetLinkedInOrganisationPosts::becauseOfMissingToken($organisation);
        }

        $parameters = [
            'author' => "urn:li:organization:{$organisation->external_id}",
            'q' => 'author',
            'count' => 50,
            'sortBy' => 'LAST_MODIFIED',
        ];

        if (! is_null($start)) {
            $parameters['start'] = $start;
        }

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$token}",
            'LinkedIn-Version' => '202501',
            'X-Restli-Protocol-Version' => '2.0.0',
        ])->get('https://api.linkedin.com/rest/posts', $parameters);

        if (! $response->successful()) {
            throw CannotGetLinkedInOrganisationPosts::becauseOfHttpErrorWithStatusCode(
                organisation: $organisation,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }
}
