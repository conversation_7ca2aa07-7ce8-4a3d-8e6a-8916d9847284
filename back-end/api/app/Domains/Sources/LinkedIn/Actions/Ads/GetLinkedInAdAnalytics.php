<?php

declare(strict_types=1);

namespace App\Domains\Sources\LinkedIn\Actions\Ads;

use App\Domains\Sources\LinkedIn\Actions\Authentication\RefreshToken;
use App\Domains\Sources\LinkedIn\Models\LinkedInAccount;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdCampaign;
use App\Domains\Sources\LinkedIn\Models\LinkedInAdInsight;
use App\Domains\Sources\LinkedIn\Support\Exceptions\Ads\CannotGetLinkedInAdAnalyticsException;
use Carbon\Carbon;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetLinkedInAdAnalytics
{
    /**
     * @param  array<string>  $campaignIds
     *
     * @throws CannotGetLinkedInAdAnalyticsException
     * @throws ConnectionException
     */
    public function execute(
        array $campaignIds,
        LinkedInAccount $linkedInAccount,
        Carbon $startDate,
        Carbon $endDate
    ): void {
        $campaigns = LinkedInAdCampaign::whereIn('external_id', $campaignIds)->get();

        if ($campaigns->isEmpty()) {
            return;
        }

        $response = $this->getResponse(
            $campaignIds,
            $linkedInAccount,
            $startDate,
            $endDate
        );

        $analytics = $response->json('elements', []);
        $this->processAnalytics($campaigns, $analytics);
    }

    protected function processAnalytics($campaigns, array $analytics): void
    {
        foreach ($analytics as $insight) {
            $campaignId = $this->extractCampaignId($insight['pivotValues'][0]);
            $campaign = $campaigns->firstWhere('external_id', $campaignId);

            if (! $campaign) {
                continue;
            }

            $date = Carbon::create(
                $insight['dateRange']['start']['year'],
                $insight['dateRange']['start']['month'],
                $insight['dateRange']['start']['day']
            );

            LinkedInAdInsight::withTrashed()->updateOrCreate(
                [
                    'linkedin_ad_campaign_id' => $campaign->id,
                    'date' => $date,
                ],
                [
                    'clicks' => $insight['clicks'] ?? 0,
                    'spend' => $insight['costInLocalCurrency'] ?? 0,
                    'impressions' => $insight['impressions'] ?? 0,
                    'reach' => $insight['approximateMemberReach'] ?? 0,
                    'ctr' => $insight['impressions'] > 0
                        ? ($insight['clicks'] / $insight['impressions']) * 100
                        : 0,
                    'cpm' => $insight['impressions'] > 0
                        ? ($insight['costInLocalCurrency'] / $insight['impressions']) * 1000
                        : 0,
                    'deleted_at' => null,
                ]
            );
        }
    }

    protected function extractCampaignId(string $pivotValue): string
    {
        return str_replace('urn:li:sponsoredCampaign:', '', $pivotValue);
    }

    /**
     * @throws CannotGetLinkedInAdAnalyticsException
     * @throws ConnectionException
     */
    protected function getResponse(
        array $campaignIds,
        LinkedInAccount $linkedInAccount,
        Carbon $startDate,
        Carbon $endDate
    ): Response {
        $token = app(RefreshToken::class)->execute($linkedInAccount);

        if (! $token) {
            throw CannotGetLinkedInAdAnalyticsException::becauseOfMissingToken($linkedInAccount);
        }

        $campaignUrns = array_map(
            fn ($id) => "urn:li:sponsoredCampaign:{$id}",
            $campaignIds
        );

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$token}",
            'LinkedIn-Version' => '202501',
            'X-Restli-Protocol-Version' => '1.0.0',
        ])->get('https://api.linkedin.com/rest/adAnalytics', [
            'q' => 'analytics',
            'pivot' => 'CAMPAIGN',
            'timeGranularity' => 'DAILY',
            'dateRange.start.year' => $startDate->year,
            'dateRange.start.month' => $startDate->month,
            'dateRange.start.day' => $startDate->day,
            'dateRange.end.year' => $endDate->year,
            'dateRange.end.month' => $endDate->month,
            'dateRange.end.day' => $endDate->day,
            'campaigns' => $campaignUrns,
            'fields' => implode(',', [
                'dateRange',
                'pivotValues',
                'impressions',
                'totalEngagements',
                'approximateMemberReach',
                'costInLocalCurrency',
                'clicks',
            ]),
        ]);

        if (! $response->successful()) {
            throw CannotGetLinkedInAdAnalyticsException::becauseOfHttpErrorWithStatusCode(
                linkedInAccount: $linkedInAccount,
                statusCode: $response->status(),
                body: $response->body()
            );
        }

        return $response;
    }
}
