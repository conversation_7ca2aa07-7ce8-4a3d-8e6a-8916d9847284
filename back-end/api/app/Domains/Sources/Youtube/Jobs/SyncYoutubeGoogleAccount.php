<?php

declare(strict_types=1);

namespace App\Domains\Sources\Youtube\Jobs;

use App\Domains\Sources\Google\Models\YoutubeChannel;
use App\Domains\Sources\Google\Models\YoutubeChannelInsight;
use App\Domains\Sources\Google\Models\YoutubeGoogleAccount;
use App\Domains\Sources\Youtube\Actions\GetChannelDetails;
use App\Domains\Sources\Youtube\Actions\GetChannelReports;
use App\Support\Scopes\SyncDeactivatedScope;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Carbon;

class SyncYoutubeGoogleAccount implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $youtubeGoogleAccountId, private readonly ?CarbonInterface $startDate = null, private readonly ?CarbonInterface $endDate = null) {}

    public function handle(): void
    {
        $youtubeGoogleAccount = YoutubeGoogleAccount::query()
            ->findOrFail($this->youtubeGoogleAccountId);

        $this->syncChannelDetails($youtubeGoogleAccount);
    }

    protected function syncChannelDetails(YoutubeGoogleAccount $youtubeGoogleAccount): void
    {
        $channels = app(GetChannelDetails::class)->execute($youtubeGoogleAccount);

        foreach ($channels as $channel) {
            $this->processChannel($youtubeGoogleAccount, $channel);
        }
    }

    protected function processChannel(YoutubeGoogleAccount $youtubeGoogleAccount, array $data): void
    {
        $youtubeChannel = YoutubeChannel::withTrashed()
            ->withoutGlobalScope(SyncDeactivatedScope::class)
            ->updateOrCreate([
                'youtube_google_account_id' => $youtubeGoogleAccount->id,
                'external_id' => $data['id'],
            ], [
                'name' => $data['snippet']['title'],
                'last_synced_at' => now(),
                'deleted_at' => null,
            ]);

        $this->processChannelInsights($youtubeChannel, $data);
        $this->getChannelReports($youtubeChannel);
    }

    protected function processChannelInsights(YoutubeChannel $youtubeChannel, array $data): void
    {
        YoutubeChannelInsight::withTrashed()->updateOrCreate([
            'youtube_channel_id' => $youtubeChannel->id,
            'date' => now()->startOfDay()->toDateTimeString(),
        ], [
            'subscriber_count' => $data['statistics']['subscriberCount'],
            'number_of_videos' => $data['statistics']['videoCount'],
            'deleted_at' => null,
        ]);
    }

    protected function getChannelReports(YoutubeChannel $youtubeChannel): void
    {
        $reports = app(GetChannelReports::class)->execute($youtubeChannel, $this->startDate, $this->endDate);

        foreach ($reports as $report) {
            $this->processChannelReport($youtubeChannel, $report);
        }
    }

    protected function processChannelReport(YoutubeChannel $youtubeChannel, array $data): void
    {
        YoutubeChannelInsight::withTrashed()->updateOrCreate([
            'youtube_channel_id' => $youtubeChannel->id,
            'date' => Carbon::createFromFormat('Y-m-d', $data['date'])->startOfDay()->toDateTimeString(),
        ], [
            'views' => $data['views'],
            'engagement' => $data['likes'] + $data['comments'] + $data['shares'],
            'deleted_at' => null,
        ]);
    }
}
