<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Instagram;

use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use App\Domains\Sources\Meta\Support\Enums\Instagram\MediaType;
use App\Domains\Sources\Meta\Support\Exceptions\Instagram\CannotGetMediaForInstagramAccount;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;

class GetInstagramMediaItems
{
    /**
     * @throws CannotGetMediaForInstagramAccount
     */
    public function execute(InstagramAccount $instagramAccount, ?string $cursor = null): ?string
    {
        $response = $this->getResponse($instagramAccount, $cursor);

        if (! $response) {
            return null;
        }

        $pages = $response->json('data', []);

        $this->processRows(
            instagramAccount: $instagramAccount,
            mediaItems: $pages
        );

        return $this->getNextPageToken($response);
    }

    protected function getNextPageToken(Response $response): ?string
    {
        $hasNextPageLink = $response->json('paging.next');
        $nextPageCursor = $response->json('paging.cursors.after');

        return $hasNextPageLink ? $nextPageCursor : null;
    }

    protected function processRows(InstagramAccount $instagramAccount, array $mediaItems): void
    {
        foreach ($mediaItems as $mediaItem) {
            $this->processRow($instagramAccount, $mediaItem);
        }
    }

    protected function processRow(InstagramAccount $instagramAccount, array $mediaItem): void
    {
        InstagramMediaItem::withTrashed()->updateOrCreate([
            'instagram_account_id' => $instagramAccount->id,
            'external_id' => $mediaItem['id'],
        ], [
            'external_date' => Carbon::parse($mediaItem['timestamp']),
            'media_type' => MediaType::from($mediaItem['media_type']),
            'media_url' => $mediaItem['thumbnail_url'] ?? $mediaItem['media_url'],
            'deleted_at' => null,
        ]);
    }

    /**
     * @throws CannotGetMediaForInstagramAccount
     */
    protected function getResponse(InstagramAccount $instagramAccount, ?string $cursor): ?Response
    {
        if (! $instagramAccount->facebookPage->token) {
            return null;
        }

        $parameters = [
            'access_token' => $instagramAccount->facebookPage->token,
            'fields' => implode(',', [
                'id',
                'media_type',
                'media_url',
                'timestamp',
                'thumbnail_url',
            ]),
        ];

        if ($cursor) {
            $parameters['after'] = $cursor;
        }

        $response = Http::get(
            url: $this->getUrl($instagramAccount),
            query: $parameters
        );

        if (! $response->successful()) {
            throw CannotGetMediaForInstagramAccount::becauseOfHttpErrorWithStatusCode(
                instagramAccount: $instagramAccount,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }

    public function getUrl(InstagramAccount $instagramAccount): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/media', $instagramAccount->external_id);
    }
}
