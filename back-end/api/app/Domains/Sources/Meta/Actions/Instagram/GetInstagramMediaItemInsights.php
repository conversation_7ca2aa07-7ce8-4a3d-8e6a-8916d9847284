<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Instagram;

use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramMediaItem;
use App\Domains\Sources\Meta\Models\InstagramMediaItemInsight;
use App\Domains\Sources\Meta\Support\Enums\Instagram\MediaType;
use App\Domains\Sources\Meta\Support\Exceptions\Instagram\CannotGetMediaInsightsForInstagramAccount;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetInstagramMediaItemInsights
{
    /**
     * @param  InstagramMediaItem[]  $instagramMediaItems
     *
     * @throws CannotGetMediaInsightsForInstagramAccount
     */
    public function execute(InstagramAccount $instagramAccount, array $instagramMediaItems): void
    {
        $response = $this->getResponse($instagramAccount, $instagramMediaItems);

        if (! $response) {
            return;
        }

        $requests = $response->json() ?? [];

        $this->processRows(
            instagramAccount: $instagramAccount,
            requests: $requests
        );
    }

    protected function processRows(InstagramAccount $instagramAccount, array $requests): void
    {
        foreach ($requests as $request) {
            if ($request['code'] !== 200) {
                continue;
            }

            $body = json_decode($request['body'], true);
            $data = $body['data'];

            if (empty($data)) {
                continue;
            }

            $this->processRow($instagramAccount, $data);
        }
    }

    protected function processRow(InstagramAccount $instagramAccount, array $mediaItemInsights): void
    {
        $externalId = explode('/', $mediaItemInsights[0]['id'])[0];
        $instagramMediaItem = InstagramMediaItem::query()
            ->where('instagram_account_id', $instagramAccount->id)
            ->where('external_id', $externalId)
            ->first();

        foreach ($mediaItemInsights as $mediaItemInsight) {
            $name = $mediaItemInsight['name'];

            if ($name === 'plays') {
                $name = 'impressions';
            }

            $previousValue = InstagramMediaItemInsight::withTrashed()
                ->where('instagram_media_item_id', $instagramMediaItem->id)
                ->whereNot('date', now()->startOfDay()->format('Y-m-d'))
                ->sum($name);
            $currentValue = $mediaItemInsight['values'][0]['value'] ?? 0;

            InstagramMediaItemInsight::withTrashed()->updateOrCreate([
                'instagram_media_item_id' => $instagramMediaItem->id,
                'date' => now()->startOfDay()->format('Y-m-d'),
            ], [
                $name => $currentValue - $previousValue,
                'deleted_at' => null,
            ]);
        }
    }

    /**
     * @param  InstagramMediaItem[]  $instagramMediaItems
     *
     * @throws CannotGetMediaInsightsForInstagramAccount
     */
    protected function getResponse(InstagramAccount $instagramAccount, array $instagramMediaItems): ?Response
    {
        if (! $instagramAccount->facebookPage->token) {
            return null;
        }

        $response = Http::post(
            url: 'https://graph.facebook.com/v23.0/',
            data: [
                'access_token' => $instagramAccount->facebookPage->token,
                'batch' => $this->getBatchRequests($instagramMediaItems),
            ]
        );

        if (! $response->successful()) {
            throw CannotGetMediaInsightsForInstagramAccount::becauseOfHttpErrorWithStatusCode(
                instagramAccount: $instagramAccount,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }

    /**
     * @param  InstagramMediaItem[]  $instagramMediaItems
     */
    public function getBatchRequests(array $instagramMediaItems): array
    {
        $requests = [];

        foreach ($instagramMediaItems as $instagramMediaItem) {
            $metrics = [
                'reach',
                'total_interactions',
            ];

            match ($instagramMediaItem->media_type) {
                MediaType::IMAGE, MediaType::CAROUSEL_ALBUM => $metrics[] = 'impressions',
                MediaType::VIDEO => $metrics[] = 'plays',
            };

            $parameters = [
                'metric' => implode(',', $metrics),
            ];

            $url = sprintf(
                '%s/insights?%s',
                $instagramMediaItem->external_id,
                http_build_query($parameters),
            );
            $requests[] = [
                'method' => 'GET',
                'relative_url' => $url,
            ];
        }

        return $requests;
    }
}
