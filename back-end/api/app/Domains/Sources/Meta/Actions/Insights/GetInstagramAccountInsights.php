<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Insights;

use App\Domains\Sources\Meta\Models\InstagramAccount;
use App\Domains\Sources\Meta\Models\InstagramAccountInsight;
use App\Domains\Sources\Meta\Support\Exceptions\Insights\CannotGetInsightsForInstagramAccount;
use Carbon\CarbonInterface;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetInstagramAccountInsights
{
    private ?CarbonInterface $date;

    /**
     * @throws CannotGetInsightsForInstagramAccount
     */
    public function execute(InstagramAccount $instagramAccount, ?string $cursor = null, ?CarbonInterface $date = null): ?string
    {
        $this->date = $date ?? now()->subDay();

        $response = $this->getResponse($instagramAccount, $cursor);

        if (! $response) {
            return null;
        }

        $dataPoints = $response->json('data', []);

        $instagramAccount->insights_last_synced_at = now();
        $instagramAccount->save();

        $this->processDataPoints(
            instagramAccount: $instagramAccount,
            dataPoints: $dataPoints
        );

        return $this->getNextPageToken($response);
    }

    protected function getNextPageToken(Response $response): ?string
    {
        $hasNextPageLink = $response->json('paging.next');
        $nextPageCursor = $response->json('paging.cursors.after');

        return $hasNextPageLink ? $nextPageCursor : null;
    }

    protected function processDataPoints(InstagramAccount $instagramAccount, array $dataPoints): void
    {
        foreach ($dataPoints as $dataPoint) {
            $this->processDataPoint(
                instagramAccount: $instagramAccount,
                dataPoint: $dataPoint
            );
        }
    }

    protected function processDataPoint(InstagramAccount $instagramAccount, array $dataPoint): void
    {
        $name = $dataPoint['name'];

        if ($name === 'views') {
            $name = 'impressions';
        }

        InstagramAccountInsight::withTrashed()->updateOrCreate([
            'instagram_account_id' => $instagramAccount->id,
            'date' => $this->date,
        ], [
            $name => $dataPoint['total_value']['value'],
            'deleted_at' => null,
        ]);
    }

    /**
     * @throws CannotGetInsightsForInstagramAccount
     */
    protected function getResponse(InstagramAccount $instagramAccount, ?string $cursor): ?Response
    {
        if (! $instagramAccount->facebookPage->token) {
            return null;
        }

        $parameters = [
            'access_token' => $instagramAccount->facebookPage->token,
            'period' => 'day',
            'since' => $this->date->format('Y-m-d'),
            'until' => $this->date->addDay()->format('Y-m-d'),
            'metric' => implode(',', [
                'reach',
                'views',
            ]),
            'metric_type' => 'total_value',
        ];

        if ($cursor) {
            $parameters['after'] = $cursor;
        }

        $response = Http::get(
            url: $this->getUrl($instagramAccount),
            query: $parameters
        );

        if (! $response->successful()) {
            throw CannotGetInsightsForInstagramAccount::becauseOfHttpErrorWithStatusCode(
                instagramAccount: $instagramAccount,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }

    public function getUrl(InstagramAccount $instagramAccount): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/insights', $instagramAccount->external_id);
    }
}
