<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Insights;

use App\Domains\Sources\Meta\Actions\Authentication\RefreshFacebookAccountToken;
use App\Domains\Sources\Meta\Models\FacebookAd;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Models\FacebookAdInsight;
use App\Domains\Sources\Meta\Models\FacebookCampaign;
use App\Domains\Sources\Meta\Models\FacebookCampaignInsight;
use App\Domains\Sources\Meta\Support\Enums\Insights\Level;
use App\Domains\Sources\Meta\Support\Exceptions\Insights\CannotGetFacebookCampaignInsightsForFacebookAdAccount;
use Carbon\CarbonInterface;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;

class GetFacebookAdInsights
{
    private ?CarbonInterface $startDate = null;

    private ?CarbonInterface $endDate = null;

    /**
     * @throws CannotGetFacebookCampaignInsightsForFacebookAdAccount
     * @throws ConnectionException
     */
    public function execute(FacebookAdAccount $facebookAdAccount, Level $level, ?string $cursor = null, ?CarbonInterface $startDate = null, ?CarbonInterface $endDate = null): ?string
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;

        $response = $this->getResponse($facebookAdAccount, $level, $cursor);
        $pages = $response->json('data', []);

        $this->processInsights(
            facebookAdAccount: $facebookAdAccount,
            level: $level,
            insights: $pages
        );

        return $this->getNextPageToken($response);
    }

    /**
     * @throws CannotGetFacebookCampaignInsightsForFacebookAdAccount
     * @throws ConnectionException
     */
    protected function getResponse(FacebookAdAccount $facebookAdAccount, Level $level, ?string $cursor): Response
    {
        $facebookAccount = $facebookAdAccount->facebookAccount ?? $facebookAdAccount->facebookBusiness->facebookAccount;
        $token = app(RefreshFacebookAccountToken::class)->execute($facebookAccount);

        if (! $token) {
            throw CannotGetFacebookCampaignInsightsForFacebookAdAccount::becauseOfMissingToken($facebookAdAccount);
        }

        $parameters = [
            'access_token' => $token,
            'limit' => 50,
            'time_increment' => 1,
            'level' => $level->value,
            'breakdowns' => 'publisher_platform',
            'time_range' => json_encode([
                'since' => ($this->startDate ?? now()->subWeeks(2))->format('Y-m-d'),
                'until' => ($this->endDate ?? now()->subDay())->format('Y-m-d'),
            ]),
            'fields' => implode(',', [
                'ad_id',
                'campaign_id',
                'campaign_name',
                'ctr',
                'reach',
                'spend',
                'clicks',
                'cpm',
                'impressions',
                'actions',
            ]),
        ];

        if ($cursor) {
            $parameters['after'] = $cursor;
        }

        $response = Http::get(
            url: $this->getUrl($facebookAdAccount),
            query: $parameters
        );

        if (! $response->successful()) {
            throw CannotGetFacebookCampaignInsightsForFacebookAdAccount::becauseOfHttpErrorWithStatusCode(
                facebookAdAccount: $facebookAdAccount,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }

    public function getUrl(FacebookAdAccount $facebookAdAccount): string
    {
        return sprintf('https://graph.facebook.com/v23.0/%s/insights', $facebookAdAccount->external_id);
    }

    protected function processInsights(FacebookAdAccount $facebookAdAccount, Level $level, array $insights): void
    {
        foreach ($insights as $insight) {
            match ($level) {
                Level::AD => $this->processAd($facebookAdAccount, $insight),
                Level::CAMPAIGN => $this->processCampaign($facebookAdAccount, $insight)
            };
        }
    }

    protected function processAd(FacebookAdAccount $facebookAdAccount, array $insight): void
    {
        $facebookCampaign = $this->updateCampaign($facebookAdAccount, $insight);
        $facebookAd = $this->updateAd($facebookAdAccount, $facebookCampaign, $insight);

        $properties = $this->basicProperties($insight);

        FacebookAdInsight::withTrashed()->updateOrCreate(
            [
                ...$properties[0],
                'facebook_ad_id' => $facebookAd->id,
            ],
            [...$properties[1], 'deleted_at' => null]
        );
    }

    protected function updateCampaign(FacebookAdAccount $facebookAdAccount, array $insight): FacebookCampaign
    {
        return FacebookCampaign::withTrashed()->updateOrCreate([
            'facebook_ad_account_id' => $facebookAdAccount->id,
            'external_id' => $insight['campaign_id'],
        ], [
            'name' => $insight['campaign_name'],
            'deleted_at' => null,
        ]);
    }

    protected function updateAd(FacebookAdAccount $facebookAdAccount, FacebookCampaign $facebookCampaign, array $insight): FacebookAd
    {
        return FacebookAd::withTrashed()->updateOrCreate([
            'facebook_ad_account_id' => $facebookAdAccount->id,
            'facebook_campaign_id' => $facebookCampaign->id,
            'external_id' => $insight['ad_id'],
        ], [
            'deleted_at' => null,
        ]);
    }

    protected function basicProperties(array $insight): array
    {
        $mappedActions = [];
        $actionTotal = 0;

        foreach (($insight['actions'] ?? []) as $action) {
            $mappedActions[$action['action_type']] = (int) $action['value'];
            $actionTotal += $action['value'];
        }

        return [
            [
                'date' => Carbon::createFromFormat('Y-m-d', $insight['date_start'])->startOfDay(),
                'publisher_platform' => $insight['publisher_platform'],
            ], [
                'ctr' => $insight['ctr'] ?? 0,
                'reach' => $insight['reach'] ?? 0,
                'spend' => $insight['spend'] ?? 0,
                'clicks' => $insight['clicks'] ?? 0,
                'cpm' => $insight['cpm'] ?? 0,
                'impressions' => $insight['impressions'] ?? 0,
                'actions' => $actionTotal,
                'action_types' => $mappedActions,
            ],
        ];
    }

    protected function processCampaign(FacebookAdAccount $facebookAdAccount, array $insight): void
    {
        $facebookCampaign = $this->updateCampaign($facebookAdAccount, $insight);

        $properties = $this->basicProperties($insight);

        FacebookCampaignInsight::withTrashed()->updateOrCreate(
            [
                ...$properties[0],
                'facebook_campaign_id' => $facebookCampaign->id,
            ],
            [...$properties[1], 'deleted_at' => null]
        );
    }

    protected function getNextPageToken(Response $response): ?string
    {
        $hasNextPageLink = $response->json('paging.next');
        $nextPageCursor = $response->json('paging.cursors.after');

        return $hasNextPageLink ? $nextPageCursor : null;
    }
}
