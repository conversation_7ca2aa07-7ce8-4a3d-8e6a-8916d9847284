<?php

declare(strict_types=1);

namespace App\Domains\Sources\Meta\Actions\Me;

use App\Domains\Sources\Meta\Actions\Authentication\RefreshFacebookAccountToken;
use App\Domains\Sources\Meta\Models\FacebookAccount;
use App\Domains\Sources\Meta\Models\FacebookAdAccount;
use App\Domains\Sources\Meta\Support\Exceptions\Me\CannotGetFacebookAdAccountsForFacebookAccount;
use App\Support\Scopes\SyncDeactivatedScope;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetFacebookAdAccounts
{
    /**
     * @throws CannotGetFacebookAdAccountsForFacebookAccount
     * @throws ConnectionException
     */
    public function execute(FacebookAccount $facebookAccount, ?string $cursor = null): ?string
    {
        $response = $this->getResponse($facebookAccount, $cursor);
        $pages = $response->json('data', []);

        $this->processRows(
            facebookAccount: $facebookAccount,
            pages: $pages
        );

        return $this->getNextPageToken($response);
    }

    protected function getNextPageToken(Response $response): ?string
    {
        $hasNextPageLink = $response->json('paging.next');
        $nextPageCursor = $response->json('paging.cursors.after');

        return $hasNextPageLink ? $nextPageCursor : null;
    }

    protected function processRows(FacebookAccount $facebookAccount, array $pages): void
    {
        foreach ($pages as $page) {
            $this->processRow($facebookAccount, $page);
        }
    }

    protected function processRow(FacebookAccount $facebookAccount, array $page): void
    {
        FacebookAdAccount::withTrashed()->withoutGlobalScope(SyncDeactivatedScope::class)->updateOrCreate(
            [
                'facebook_account_id' => $facebookAccount->id,
                'external_id' => $page['id'],
            ],
            [
                'name' => $page['name'],
                'last_synced_at' => now(),
                'deleted_at' => null,
            ]
        );
    }

    /**
     * @throws CannotGetFacebookAdAccountsForFacebookAccount
     * @throws ConnectionException
     */
    protected function getResponse(FacebookAccount $facebookAccount, ?string $cursor): Response
    {
        $token = app(RefreshFacebookAccountToken::class)->execute($facebookAccount);

        if (! $token) {
            throw CannotGetFacebookAdAccountsForFacebookAccount::becauseOfMissingToken($facebookAccount);
        }

        $parameters = [
            'access_token' => $token,
            'limit' => 50,
            'fields' => implode(',', [
                'name',
            ]),
        ];

        if ($cursor) {
            $parameters['after'] = $cursor;
        }

        $response = Http::get(
            url: $this->getUrl(),
            query: $parameters
        );

        if (! $response->successful()) {
            throw CannotGetFacebookAdAccountsForFacebookAccount::becauseOfHttpErrorWithStatusCode(
                facebookAccount: $facebookAccount,
                statusCode: $response->status(),
                body: $response->body(),
            );
        }

        return $response;
    }

    public function getUrl(): string
    {
        return 'https://graph.facebook.com/v23.0/me/adaccounts';
    }
}
