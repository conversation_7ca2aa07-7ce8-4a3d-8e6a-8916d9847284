<?php

declare(strict_types=1);

namespace App\Domains\Sources\Google\Actions\Ads;

use App\Domains\Sources\Google\Actions\Authentication\RefreshToken;
use App\Domains\Sources\Google\Models\GoogleAdAccount;
use App\Domains\Sources\Google\Models\GoogleAdCampaign;
use App\Domains\Sources\Google\Models\GoogleAdCampaignInsight;
use App\Domains\Sources\Google\Support\Exceptions\Ads\CannotGetCampaignsWithMetricsException;
use Carbon\CarbonInterface;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class GetCampaignsWithMetrics
{
    private ?CarbonInterface $startDate;

    private ?CarbonInterface $endDate;

    /**
     * @throws CannotGetCampaignsWithMetricsException
     * @throws ConnectionException
     */
    public function execute(GoogleAdAccount $googleAdAccount, ?CarbonInterface $startDate = null, ?CarbonInterface $endDate = null): void
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;

        $response = $this->getResponse($googleAdAccount);

        $results = $response->json('0.results', []);

        $this->processResults(
            googleAdAccount: $googleAdAccount,
            results: $results
        );
    }

    protected function getGoogleAdCampaign(GoogleAdAccount $googleAdAccount, array $result): GoogleAdCampaign
    {
        return GoogleAdCampaign::withTrashed()->updateOrCreate([
            'google_ad_account_id' => $googleAdAccount->id,
            'external_id' => (int) basename($result['campaign']['resourceName']),
        ], [
            'name' => $result['campaign']['name'],
            'advertising_channel_type' => $result['campaign']['advertisingChannelType'],
            'deleted_at' => null,
        ]);
    }

    protected function processResults(GoogleAdAccount $googleAdAccount, array $results): void
    {
        foreach ($results as $result) {
            $googleAdCampaign = $this->getGoogleAdCampaign(
                googleAdAccount: $googleAdAccount,
                result: $result
            );

            $this->processResult(
                googleAdCampaign: $googleAdCampaign,
                result: $result,
            );
        }
    }

    protected function processResult(GoogleAdCampaign $googleAdCampaign, array $result): void
    {
        GoogleAdCampaignInsight::withTrashed()->updateOrCreate([
            'google_ad_campaign_id' => $googleAdCampaign->id,
            'date' => $result['segments']['date'],
        ], [
            'clicks' => $result['metrics']['clicks'] ?? 0,
            'video_views' => $result['metrics']['videoViews'] ?? 0,
            'ctr' => $result['metrics']['ctr'] ?? 0,
            'impressions' => $result['metrics']['impressions'] ?? 0,
            'average_cpc' => (string) round(($result['metrics']['averageCpc'] ?? '0') / 1000000, 4),
            'average_cpm' => (string) round(($result['metrics']['averageCpm'] ?? '0') / 1000000, 4),
            'spend' => (string) round(($result['metrics']['costMicros'] ?? 0) / 1000000, 4),
            'deleted_at' => null,
        ]);
    }

    /**
     * @throws CannotGetCampaignsWithMetricsException
     * @throws ConnectionException
     */
    protected function getResponse(GoogleAdAccount $googleAdAccount): Response
    {
        $token = app(RefreshToken::class)->execute($googleAdAccount->googleAccount);

        if (! $token) {
            throw CannotGetCampaignsWithMetricsException::becauseOfMissingToken($googleAdAccount);
        }

        $headers = [
            'Developer-token' => config('services.google.ads_developer_token'),
        ];

        if ($googleAdAccount->customer_id) {
            $headers['Login-customer-id'] = $googleAdAccount->customer_id;
        }

        $response = Http::withToken($token)
            ->withHeaders($headers)
            ->post(
                url: sprintf('https://googleads.googleapis.com/v18/customers/%s/googleAds:searchStream', $googleAdAccount->external_id),
                data: [
                    'query' => sprintf(<<<'SQL'
                        SELECT
                            campaign.id,
                            campaign.name,
                            campaign.advertising_channel_type,
                            metrics.clicks,
                            metrics.ctr,
                            metrics.impressions,
                            metrics.average_cpc,
                            metrics.average_cpm,
                            metrics.video_views,
                            metrics.cost_micros,
                            segments.date
                        FROM
                            campaign
                        WHERE
                            segments.date BETWEEN '%s' AND '%s'
                        ORDER BY
                            segments.date
                    SQL,
                        ($this->startDate ?? now()->subDays(4))->format('Y-m-d'),
                        ($this->endDate ?? now()->subDay())->format('Y-m-d'),
                    ),
                ]
            );

        if (! $response->successful()) {
            throw CannotGetCampaignsWithMetricsException::becauseOfHttpErrorWithStatusCode(
                googleAdAccount: $googleAdAccount,
                statusCode: $response->status(),
                body: $response->body()
            );
        }

        return $response;
    }
}
