<?php

namespace App\Http\Dashboard\Controllers;

use App\Domains\Dashboard\Models\BusinessUnit;
use App\Domains\Dashboard\Models\DataSource;
use App\Http\Dashboard\Requests\BusinessUnitRequest;
use App\Http\Dashboard\Resources\BusinessUnitResource;
use App\Support\Controller;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class BusinessUnitController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        $units = BusinessUnit::query()->get();

        return BusinessUnitResource::collection($units);
    }

    public function store(BusinessUnitRequest $request): BusinessUnitResource
    {
        $unit = BusinessUnit::query()->create([
            'name' => $request->getName()
        ]);

        return BusinessUnitResource::make($unit);
    }

    public function update(BusinessUnitRequest $request, int $id): BusinessUnitResource
    {
        $unit = BusinessUnit::query()->findOrFail($id);

        DataSource::query()->where('business_unit', $unit->name)->update([
            'business_unit' => $request->getName()
        ]);

        $unit->name = $request->getName();
        $unit->save();

        return BusinessUnitResource::make($unit);
    }

}
