<?php

namespace App\Http\Dashboard\Controllers;

use App\Domains\Dashboard\Models\DataSource;
use App\Domains\Dashboard\Models\Region;
use App\Http\Dashboard\Requests\RegionRequest;
use App\Http\Dashboard\Resources\RegionResource;
use App\Support\Controller;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class RegionController extends Controller
{
    public function index(): AnonymousResourceCollection
    {
        $units = Region::query()->get();

        return RegionResource::collection($units);
    }

    public function store(RegionRequest $request): RegionResource
    {
        $unit = Region::query()->create([
            'name' => $request->getName()
        ]);

        return RegionResource::make($unit);
    }

    public function update(RegionRequest $request, int $id): RegionResource
    {
        $unit = Region::query()->findOrFail($id);

        DataSource::query()->where('region', $unit->name)->update([
            'region' => $request->getName()
        ]);

        $unit->name = $request->getName();
        $unit->save();

        return RegionResource::make($unit);
    }

}
