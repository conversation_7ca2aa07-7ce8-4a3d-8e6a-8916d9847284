<?php

namespace App\Http\Dashboard\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RegionRequest extends FormRequest
{
    public const REQUEST_NAME = 'name';

    public function rules(): array
    {
        return [
            self::REQUEST_NAME => ['required', 'string']
        ];
    }

    public function getName(): string
    {
        return $this->input(self::REQUEST_NAME);
    }
}
