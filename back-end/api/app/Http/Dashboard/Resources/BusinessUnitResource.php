<?php

namespace App\Http\Dashboard\Resources;

use App\Domains\Dashboard\Models\BusinessUnit;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BusinessUnitResource extends JsonResource
{
    public const RESPONSE_ID = 'id';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_CREATED_AT = 'created_at';
    public const RESPONSE_UPDATED_AT = 'updated_at';

    /** @var BusinessUnit $resource */
    public $resource;

    public function toArray(Request $request): array
    {
        return [
            self::RESPONSE_ID => $this->resource->id,
            self::RESPONSE_NAME => $this->resource->name,
            self::RESPONSE_CREATED_AT => $this->resource->created_at,
            self::RESPONSE_UPDATED_AT => $this->resource->updated_at
        ];
    }
}
