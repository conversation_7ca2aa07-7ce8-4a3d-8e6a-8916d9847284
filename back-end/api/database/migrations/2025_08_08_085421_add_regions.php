<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        $regions = DB::table('data_sources')->select('region')->groupBy('region')->get();

        foreach ($regions as $region) {
            DB::table('regions')->insert(
                [
                    'name' => $region->region,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }
    }
};
