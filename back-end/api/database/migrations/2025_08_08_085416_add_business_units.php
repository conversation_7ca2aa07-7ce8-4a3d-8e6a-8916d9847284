<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        $units = DB::table('data_sources')->select('business_unit')->groupBy('business_unit')->get();

        foreach ($units as $unit) {
            DB::table('business_units')->insert(
                [
                    'name' => $unit->business_unit,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }
    }
};
